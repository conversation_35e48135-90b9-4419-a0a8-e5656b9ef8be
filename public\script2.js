// Initialize Firebase
const firebaseConfig = {
  apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
  authDomain: "barefoot-elearning-app.firebaseapp.com",
  projectId: "barefoot-elearning-app",
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
  storageBucket: "barefoot-elearning-app.appspot.com",
  messagingSenderId: "170819735788",
  appId: "1:170819735788:web:223af318437eb5d947d5c9"
};

firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();

let userCompany = "";
let emailValidationTimeout = null;
let emailDebounceTimer = null;
let roleDebounceTimer = null;
const DEBOUNCE_DELAY = 1000;
let proceedButtonListenerAdded = false;

// Flag to track intentional navigation
let intentionalNavigation = false;

// Flag to track if a new assessment has been started
// This will be used to ensure we don't find old assessments
window.newAssessmentStarted = false;

// Add functions to manage the beforeunload event handler
function setupBeforeUnloadHandler() {
  // First remove any existing handler to avoid duplicates
  window.removeEventListener('beforeunload', handleBeforeUnload);

  // Then add the handler
  window.addEventListener('beforeunload', handleBeforeUnload);

  console.log('beforeunload handler set up');
}

// Define the handler function separately so we can add/remove it
async function handleBeforeUnload(event) {
  // Skip the warning if navigation is intentional
  if (intentionalNavigation) {
    return;
  }

  if (!isFinalSuccessContainerDisplayed && !isFailureContainerDisplayed) {
    const email = document.getElementById("email").value.trim();

    if (email && userCompany && userCompany.trim() !== '') {
      try {
        // Only prevent default and show message here
        event.preventDefault();
        event.returnValue = 'You have an assessment in progress. If you leave this page, your progress will be lost.';

        const companyRef = db.collection('companies').doc(userCompany);
        const userRef = companyRef.collection('users').doc(email);

        try {
          await userRef.update({
            status: 'aborted',
            abortedAt: firebase.firestore.FieldValue.serverTimestamp()
          });
          console.log('Assessment status updated to aborted');
        } catch (updateError) {
          console.error('Error updating assessment status to Firestore:', updateError);
        }
      } catch (error) {
        console.error('Error in beforeunload event:', error);
      }
    }
  }
}

// Call this function to remove the beforeunload handler when not needed
function removeBeforeUnloadHandler() {
  window.removeEventListener('beforeunload', handleBeforeUnload);
  console.log('beforeunload handler removed');
}

// Debounce function to prevent multiple rapid calls
function debounce(func, wait) {
  let timeout;
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

let debounceTimer;
const commonRoles = [
  "Administrative Assistant",
  "Marketing Executive",
  "Sales Executive",
  "Project Manager",
  "Business Analyst",
  "Data Analyst",
  "Customer Service Advisor",
  "Operations Coordinator",
  "Finance Analyst",
  "HR Officer",
  "Product Manager",
  "Account Manager",
  "Executive Assistant",
  "IT Support Technician",
  "CEO"
];


let isEmailValid = false;
let isRoleValid = false;
let app;

function updateSubmitButton() {
  const submitButton = document.querySelector('#user-form button[type="submit"]');
  if (submitButton) {
      // For submission-based validation, enable button when both fields have content
      const emailInput = document.getElementById('email');
      const roleInput = document.getElementById('role');
      const firstNameInput = document.getElementById('first-name');
      const lastNameInput = document.getElementById('last-name');

      const hasRequiredFields = emailInput?.value.trim() &&
                               roleInput?.value.trim() &&
                               firstNameInput?.value.trim() &&
                               lastNameInput?.value.trim();

      submitButton.disabled = !hasRequiredFields;

      if (submitButton.disabled) {
          submitButton.classList.add('opacity-50', 'cursor-not-allowed');
          submitButton.classList.remove('hover:bg-blue-600');
      } else {
          submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
          submitButton.classList.add('hover:bg-blue-600');
      }
  }
}

const initializeFirebase = () => {
  app = firebase.initializeApp(firebaseConfig);
  const db = firebase.firestore(app);
};

window.onload = () => {
  initializeFirebase();
  const startBtn = document.getElementById("start-btn");
  if (startBtn) {
    startBtn.addEventListener("click", startQuiz);
  }
};


function createRoleSuggestions() {
  const roleInput = document.getElementById('role');
  if (!roleInput) return;

  const suggestionsContainer = document.createElement('div');
  suggestionsContainer.className = 'role-suggestions';
  suggestionsContainer.style.display = 'none';

  // Add inner scroll container
  const scrollContainer = document.createElement('div');
  scrollContainer.className = 'role-suggestions-scroll';
  suggestionsContainer.appendChild(scrollContainer);

  roleInput.parentElement.appendChild(suggestionsContainer);

  function updateSuggestions(searchTerm) {
    const filteredRoles = commonRoles.filter(role =>
      role.toLowerCase().includes(searchTerm.toLowerCase())
    );

    scrollContainer.innerHTML = '';

    if (filteredRoles.length === 0) {
      suggestionsContainer.style.display = 'none';
      return;
    }
    filteredRoles.forEach(role => {
      const tab = document.createElement('div');
      tab.textContent = role;
      tab.className = 'suggestion-tab';

      tab.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Update input value
        roleInput.value = role;

        // Hide suggestions
        suggestionsContainer.style.display = 'none';

        // Trigger validation
        if (typeof validateRole === 'function') {
          validateRole(role);
        }
      });

      scrollContainer.appendChild(tab);
    });

    // Only show container if there are suggestions
    suggestionsContainer.style.display = 'block';
  }

  // Input event for filtering
  roleInput.addEventListener('input', (e) => {
    const searchTerm = e.target.value.trim();

    if (searchTerm === '') {
      suggestionsContainer.style.display = 'none';
    } else {
      updateSuggestions(searchTerm);
    }
  });

  // Focus event to show suggestions
  roleInput.addEventListener('focus', () => {
    const searchTerm = roleInput.value.trim();
    if (searchTerm) {
      updateSuggestions(searchTerm);
    } else {
      // Show all suggestions on focus if input is empty
      updateSuggestions('');
    }
  });

  // Click outside to close suggestions
  document.addEventListener('click', (e) => {
    if (!roleInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
      suggestionsContainer.style.display = 'none';
    }
  });
}

// Make sure to call this when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  createRoleSuggestions();
});



const sectionNames = ["Essentials", "Intermediate", "Advanced", "Champions"];
let currentQuestion = 0;
let score = 0;
let quizData = [];
let currentSection = 1;
let userName = "";
let selectedOption = "";

// Progressive loading variables
let totalExpectedQuestions = 15; // 10 knowledge-check + 5 self-assessment
let isProgressiveLoadingEnabled = true;
let backgroundLoadingInProgress = false;
let firstBatchLoaded = false;
let firstBatchSize = 4; // Number of questions to load in first batch

// Make totalExpectedQuestions globally available
window.totalExpectedQuestions = totalExpectedQuestions;

// Progressive loading control functions for testing
window.enableProgressiveLoading = function() {
  window.isProgressiveLoadingEnabled = true;
  console.log('Progressive loading enabled');
};

window.disableProgressiveLoading = function() {
  window.isProgressiveLoadingEnabled = false;
  console.log('Progressive loading disabled - will use full loading');
};

window.setFirstBatchSize = function(size) {
  window.firstBatchSize = Math.max(1, Math.min(size, 10));
  console.log('First batch size set to:', window.firstBatchSize);
};

const sectionScores = {
  "Essentials": 0,
  "Intermediate": 0,
  "Advanced": 0,
  "Champions": 0
};
const questionsPerSection = {
  "Essentials": 0,
  "Intermediate": 0,
  "Advanced": 0,
  "Champions": 0
};

let isFinalSuccessContainerDisplayed = false;
let isFailureContainerDisplayed = false;
const FEEDBACK_DELAY = 1300;
const AUTO_NEXT_DELAY = 1350;



// Clear email validation errors when user starts typing
document.getElementById('email').addEventListener('input', (event) => {
    clearEmailValidationError();
    updateSubmitButton(); // Update button state based on field content
});



document.addEventListener('DOMContentLoaded', () => {
  resetValidationState();

  // Initialize submit button state
  updateSubmitButton();

  // Override the original loadQuizData function to set up button handlers
  const originalLoadQuizData = window.loadQuizData;
  window.loadQuizData = async function() {
    // Call the original function first
    await originalLoadQuizData();

    // Then set up our button handlers
    console.log('Setting up button handlers after quiz data loaded');
    setupButtonHandlers();
    setupSkipButtonHandler();
  };
});




function showConsentContainer() {
  document.getElementById("consent-container").classList.remove("hidden");
}

function hideConsentContainer() {
  document.getElementById("consent-container").classList.add("hidden");
}

document.getElementById("start-btn").addEventListener("click", () => {
  showConsentContainer();
});



document.getElementById("consent-btn").addEventListener("click", () => {
  const consentCheckbox = document.getElementById("consent-checkbox");
  if (consentCheckbox.checked) {
    // Set intentional navigation flag
    intentionalNavigation = true;

    hideConsentContainer();
    document.getElementById("user-form-container").style.display = "block";
    document.getElementById("start-page").style.display = "none";
  } else {
    alert("Please agree to the privacy policy to proceed.");
  }
});

document.getElementById("close-consent-btn").addEventListener("click", () => {
  const consentCheckbox = document.getElementById("consent-checkbox");
  if (!consentCheckbox.checked) {
    const confirmResult = confirm("To ensure your privacy is protected, as required by GDPR, please agree to our Privacy Policy before proceeding with the assessment. If you'd prefer not to proceed, click 'OK' to restart the assessment.");
    if (confirmResult) {
      const quitConfirmation = confirm("Are you sure you want to quit the assessment? Pressing OK will restart the app and erase all your progress.");
      if (quitConfirmation) {
        window.location.reload();
      }
    }

  } else {
    const proceedConfirmation = confirm("You have agreed to the Privacy Policy. Click 'OK' to proceed with the assessment or 'Cancel' to uncheck the consent checkbox.");
    if (proceedConfirmation) {

      hideConsentContainer();
      document.getElementById("user-form-container").style.display = "block";
    } else {

      consentCheckbox.checked = false;
    }
  }
});



// This event listener is already added in window.onload

document.getElementById("next-btn").addEventListener("click", () => {
  currentQuestion++;
  if (currentQuestion < quizData.length) {
    loadQuestion();
    updateProgressBar();
  } else {
    endQuiz();
  }
});

// Setup button handlers without replacing the buttons
function setupButtonHandlers() {
  for (let i = 0; i < 4; i++) {
    const btn = document.getElementById(`btn${i}`);

    // Remove existing click handlers to avoid duplicates
    const newBtn = btn.cloneNode(false); // Clone without events but keep the ID
    while (btn.firstChild) {
      newBtn.appendChild(btn.firstChild);
    }
    btn.parentNode.replaceChild(newBtn, btn);

    // Add our click handler
    newBtn.addEventListener("click", (event) => {
    selectedOption = event.target;
    const questionObj = quizData[currentQuestion];

    if (questionObj.type === "self-assessment") {
      // For self-assessment, just highlight the selected option
      selectedOption.className = "option-btn self-assessment-selected";
      document.getElementById("message").innerHTML = '<span class="response-recorded">Response recorded</span>';

      // No score increment for self-assessment questions
    } else {
      // For knowledge check questions, check correctness but don't show feedback
      const isCorrect = questionObj.options[i] === questionObj.answer;

      if (isCorrect) {
        sectionScores[sectionNames[currentSection - 1]]++;
        score++;
        document.getElementById("score").innerText = score;
        // Use the same styling for all answers to avoid showing correctness
        selectedOption.className = "option-btn correct";
        document.getElementById("message").innerHTML = '<span class="response-recorded">Response recorded</span>';
      } else {
        selectedOption.className = "option-btn wrong";
        document.getElementById("message").innerHTML = '<span class="response-recorded">Response recorded</span>';
      }
    }

    // Disable all buttons after answer for both question types
    for (let j = 0; j < 4; j++) {
      const btn = document.getElementById(`btn${j}`);
      if (btn.style.display !== "none") {  // Only disable visible buttons
        btn.disabled = true;
        btn.style.cursor = "not-allowed";
        btn.style.opacity = questionObj.options[j] === selectedOption.innerText ? 1 : 0.5;
      }
    }

    document.getElementById("skip-btn").disabled = true;
    document.getElementById("skip-btn").style.cursor = "not-allowed";
    document.getElementById("skip-btn").style.opacity = 0.5;

    updateProgressBar();

    // Log the response using the quizLogger
    const userResponse = {
      question: questionObj.question,
      selectedAnswer: questionObj.options[i],
      correctAnswer: questionObj.type === "knowledge-check" ? questionObj.answer : null,
      isCorrect: questionObj.type === "knowledge-check" ? questionObj.options[i] === questionObj.answer : null,
      questionNumber: currentQuestion + 1,
      questionType: questionObj.type,
      skillLevel: questionObj.type === "self-assessment" ? parseInt(selectedOption.dataset.level) : null,
      skillArea: questionObj.skillArea || questionObj.course || "General",
      section: sectionNames[currentSection - 1],
      timestamp: new Date().toISOString()
    };

    if (window.quizLogger && typeof window.quizLogger.logUserResponse === 'function') {
      window.quizLogger.logUserResponse(userResponse);
    }

    setTimeout(() => {
      setTimeout(() => {
        currentQuestion++;
        if (currentQuestion < quizData.length) {
          loadQuestion();
          updateProgressBar();
        } else {
          endQuiz();
        }
      }, AUTO_NEXT_DELAY - FEEDBACK_DELAY);
    }, FEEDBACK_DELAY);
  });
  }
}

// Update skip button handler
function setupSkipButtonHandler() {
  const skipBtn = document.getElementById("skip-btn");

  // Remove existing click handlers to avoid duplicates
  const newSkipBtn = skipBtn.cloneNode(false); // Clone without events
  while (skipBtn.firstChild) {
    newSkipBtn.appendChild(skipBtn.firstChild);
  }
  skipBtn.parentNode.replaceChild(newSkipBtn, skipBtn);

  // Add our click handler
  newSkipBtn.addEventListener("click", () => {
  const questionObj = quizData[currentQuestion];

  // Log the skipped response
  const userResponse = {
    question: questionObj.question,
    selectedAnswer: 'SKIPPED',
    correctAnswer: questionObj.type === "knowledge-check" ? questionObj.answer : null,
    isCorrect: false, // Skipping is never correct
    questionNumber: currentQuestion + 1,
    questionType: questionObj.type,
    skillLevel: null, // No skill level for skipped questions
    skillArea: questionObj.skillArea || questionObj.course || "General",
    section: sectionNames[currentSection - 1],
    timestamp: new Date().toISOString()
  };

  if (window.quizLogger && typeof window.quizLogger.logUserResponse === 'function') {
    window.quizLogger.logUserResponse(userResponse);
  }

  currentQuestion++;
  if (currentQuestion < quizData.length) {
    loadQuestion();
    updateProgressBar();
  } else {
    endQuiz();
  }
});
}

document.getElementById("next-section-btn").addEventListener("click", () => {
  if (currentSection < sectionNames.length) { // Check if there is a next section
    currentSection++;
    document.getElementById("success-container").style.display = "none";
    document.getElementById("quiz-container").style.display = "block";
    currentQuestion = 0;
    score = 0;
    document.getElementById("score").innerText = "0";
    loadQuizData();
  }
});

// Set up the beforeunload handler
setupBeforeUnloadHandler();

window.SkillsGapAnalysis = {
  async fetchData(email, company) {
      try {
          // Validate inputs
          if (!email || !company) {
              throw new Error('Missing required parameters: email or company');
          }

          const db = firebase.firestore();
          const data = await fetchAssessmentData(email, company);

          if (!data || !data.report || !data.report.competencyAnalysis) {
              throw new Error('Invalid assessment data structure');
          }

          return data;
      } catch (error) {
          console.error('Error fetching skills gap data:', error);
          throw error;
      }
  },

  async showAnalysis(data) {
      try {
          if (!data || !data.report || !data.report.competencyAnalysis) {
              throw new Error('Invalid data structure for skills gap analysis');
          }

          // Show the modal with the provided data
          await showSkillsGapAnalysis(data);
      } catch (error) {
          console.error('Error showing skills gap analysis:', error);
          throw error;
      }
  }
};

// Update the pathway button's styles and state management
function initializePathwayButton() {
  const pathwayBtn = document.getElementById("pathway-btn");
  if (!pathwayBtn) {
    console.warn('Pathway button not found in DOM');
    return;
  }

  // Set initial state
  pathwayBtn.innerHTML = `
    <span class="button-text processing-text">
      Processing skills gap analysis 0%
    </span>
  `;
  pathwayBtn.disabled = true;
  pathwayBtn.classList.add('opacity-50', 'cursor-not-allowed');
}

function updateButtonProgress(progress) {
  const pathwayBtn = document.getElementById("pathway-btn");
  if (!pathwayBtn) return;

  const buttonText = pathwayBtn.querySelector('.button-text');
  if (buttonText) {
    buttonText.textContent = `Processing skills gap analysis ${Math.round(progress)}%`;
  }
}

function enablePathwayButton() {
  const pathwayBtn = document.getElementById("pathway-btn");
  if (!pathwayBtn) return;

  if (!window.recommendationsReady) {
    pathwayBtn.disabled = true;
    pathwayBtn.classList.add('opacity-50', 'cursor-not-allowed');
    pathwayBtn.querySelector('.button-text').textContent = 'Processing results...';
    return;
  }

  // If we do have recommendations, enable the button
  pathwayBtn.disabled = false;
  pathwayBtn.classList.remove('opacity-50', 'cursor-not-allowed');
  pathwayBtn.querySelector('.button-text').textContent = 'View Skills Gap Analysis';
}


async function checkRecommendations(email, company) {
  try {
    // If a new assessment has been started but we haven't received recommendations yet,
    // don't allow old recommendations to be found
    if (window.newAssessmentStarted) {
      console.log('New assessment has been started - checking for fresh recommendations only');
    }

    const userRef = db.collection('companies')
                     .doc(company)
                     .collection('users')
                     .doc(email);

    const doc = await userRef.get();
    if (!doc.exists) return false;

    const data = doc.data();

    // Extract dates for clearer comparison
    const assessmentDate = data.lastSoftSkillsAssessmentDate ? data.lastSoftSkillsAssessmentDate.toDate() : null;
    const startTime = window.currentAssessmentStartTime;

    // Log timestamps for debugging
    console.log('Timestamp comparison:', {
      assessmentTimestamp: assessmentDate ? assessmentDate.toISOString() : null,
      sessionStartTimestamp: startTime ? startTime.toISOString() : null,
      difference: assessmentDate && startTime ? (assessmentDate - startTime) / 1000 + ' seconds' : null,
      newAssessmentStarted: window.newAssessmentStarted
    });

    console.log('Checking recommendations for user data:', {
      hasLastSoftSkillsAssessmentId: !!data.lastSoftSkillsAssessmentId,
      hasLastSoftSkillsAssessmentDate: !!data.lastSoftSkillsAssessmentDate,
      hasSoftSkillsCourseRecommendations: Array.isArray(data.softSkillsCourseRecommendations) && data.softSkillsCourseRecommendations.length > 0,
      // Not using courseRecommendations for soft skills validation anymore
      currentAssessmentStartTime: window.currentAssessmentStartTime ? window.currentAssessmentStartTime.toISOString() : null,
      lastSoftSkillsAssessmentDate: data.lastSoftSkillsAssessmentDate ? data.lastSoftSkillsAssessmentDate.toDate().toISOString() : null,
      newAssessmentStarted: window.newAssessmentStarted
    });

    // Check for soft skills assessment ID and recommendations
    const hasSoftSkillsAssessmentId = !!data.lastSoftSkillsAssessmentId;

    // Check if soft skills recommendations arrays exist AND have items
    // ONLY check softSkillsCourseRecommendations to avoid using digital skills data
    const hasSoftSkillsRecommendations = hasSoftSkillsAssessmentId &&
      (Array.isArray(data.softSkillsCourseRecommendations) && data.softSkillsCourseRecommendations.length > 0);

    // If we don't have recommendations, return false immediately
    if (!hasSoftSkillsRecommendations) {
      console.log('No recommendations found');
      window.recommendationsReady = false;
      return false;
    }

    // More stringent session validation
    const isCurrentSession = assessmentDate &&
                            startTime &&
                            assessmentDate > startTime;

    // Add a minimum time difference requirement to prevent false positives
    const minimumSecondsDifference = 5; // At least 5 seconds between start and result
    const hasValidTimeDifference = assessmentDate &&
                                 startTime &&
                                 ((assessmentDate - startTime) / 1000) > minimumSecondsDifference;

    // If a new assessment has been started, we need to check if the recommendations
    // were generated AFTER the assessment was started
    let hasValidRecommendations = false;

    if (window.newAssessmentStarted) {
      // Only accept recommendations that were generated after the new assessment was started
      // AND meet all other validation criteria
      hasValidRecommendations = hasSoftSkillsRecommendations &&
                              isCurrentSession &&
                              hasValidTimeDifference;

      // If we find valid recommendations, we can reset the flag
      if (hasValidRecommendations) {
        console.log('Found valid recommendations for the new assessment - resetting flag');
        window.newAssessmentStarted = false;
      }
    } else {
      // If no new assessment has been started, use the normal validation
      hasValidRecommendations = hasSoftSkillsRecommendations &&
                              isCurrentSession &&
                              hasValidTimeDifference;
    }

    console.log('Recommendation check result:', {
      hasSoftSkillsAssessmentId,
      hasSoftSkillsRecommendations,
      isCurrentSession,
      hasValidTimeDifference,
      hasValidRecommendations,
      newAssessmentStarted: window.newAssessmentStarted
    });

    // Update global state
    window.recommendationsReady = hasValidRecommendations;

    return hasValidRecommendations;
  } catch (error) {
    console.error('Error checking recommendations:', error);
    window.recommendationsReady = false;
    return false;
  }
}

function pollForRecommendations(email, company) {
  // Prevent multiple simultaneous polling intervals
  if (window.pollInterval) {
    console.log('Polling already in progress');
    return;
  }

  console.log('Starting recommendations polling for:', { email, company });

  let progress = 0;
  // This interval updates the button text to show a "fake" progress up to 85%
  const progressInterval = setInterval(() => {
    if (progress < 85) {
      progress += Math.random() * 5;
      progress = Math.min(85, progress);
      updateButtonProgress(progress);
    }
  }, 2000);

  let attempts = 0;
  const maxAttempts = 60; // 2 minutes if polling every 2 seconds

  // The main polling interval
  window.pollInterval = setInterval(async () => {
    console.log('Checking for recommendations...');
    attempts++;

    // This checks Firestore to see if the user doc has courseRecommendations
    const hasRecommendations = await checkRecommendations(email, company);

    if (hasRecommendations) {
      console.log('Recommendations found! Enabling button...');
      clearInterval(window.pollInterval);
      clearInterval(progressInterval);
      window.pollInterval = null;
      window.isPollingStarted = false;

      // **Show success notification before completing final progress**
      showNotification('Your results are processed successfully', 'success');

      // Smoothly complete the progress from 85% to 100%
      let finalProgress = progress;
      const completeProgress = setInterval(() => {
        if (finalProgress < 100) {
          finalProgress += 3;
          updateButtonProgress(Math.min(100, finalProgress));
        } else {
          clearInterval(completeProgress);
          // Small delay to let the user see 100%
          setTimeout(() => {
            // Mark recommendations as ready and enable the button
            window.recommendationsReady = true;
            enablePathwayButton();
          }, 500);
        }
      }, 100);

    } else if (attempts >= maxAttempts) {
      // If we can’t find any recommendations within maxAttempts, stop polling
      clearInterval(window.pollInterval);
      clearInterval(progressInterval);
      window.pollInterval = null;
      window.isPollingStarted = false;

      // Show error and hide the button (or handle as you wish)
      showNotification('Unable to process results. Please try again later.', 'error');
      const pathwayBtn = document.getElementById("pathway-btn");
      if (pathwayBtn) {
        pathwayBtn.style.display = 'none';
      }
    }
  }, 2000);
}

document.getElementById("pathway-btn").addEventListener("click", async () => {
  const pathwayBtn = document.getElementById("pathway-btn");
  if (pathwayBtn.disabled || !window.recommendationsReady) return;

  try {
    pathwayBtn.disabled = true;
    pathwayBtn.classList.add('opacity-50', 'cursor-not-allowed');
    pathwayBtn.querySelector('.button-text').textContent = 'Loading analysis...';

    showModalLoadingOverlay();

    const email = document.getElementById("email").value.trim();

    if (!email || !userCompany) {
      throw new Error('Missing user information');
    }

    const skillsData = await window.SkillsGapAnalysis.fetchData(email, userCompany);
    if (skillsData) {
      await window.SkillsGapAnalysis.showAnalysis(skillsData);
    } else {
      // If no data, just enable the button without showing an error
      console.log('No skills data available yet');
    }

  } catch (error) {
    console.error('Error showing skills gap analysis:', error);
    // Don't show error notification, just log it
  } finally {
    hideModalLoadingOverlay();
    if (window.recommendationsReady) {
      enablePathwayButton();
    }
  }
});

// This event listener is a duplicate of the one above

// End of duplicate event listener

async function fetchAssessmentData(userEmail, userCompany) {
  try {
    const userRef = db.collection('companies')
                     .doc(userCompany)
                     .collection('users')
                     .doc(userEmail);

    // Get user document for latest assessment ID
    const userDoc = await userRef.get();
    if (!userDoc.exists) {
      throw new Error('User not found');
    }

    const userData = userDoc.data();
    const lastSoftSkillsAssessmentId = userData.lastSoftSkillsAssessmentId || userData.lastAssessmentId;

    if (!lastSoftSkillsAssessmentId) {
      throw new Error('No assessment found');
    }

    // Try to get soft skills data first, fall back to digital if needed
    let assessmentDoc;
    if (userData.lastSoftSkillsAssessmentId) {
      assessmentDoc = await userRef
        .collection('softSkillsAssessmentResults')
        .doc(userData.lastSoftSkillsAssessmentId)
        .get();
    } else if (userData.lastAssessmentId) {
      assessmentDoc = await userRef
        .collection('assessmentResults')
        .doc(userData.lastAssessmentId)
        .get();
    }

    if (!assessmentDoc || !assessmentDoc.exists) {
      throw new Error('Assessment results not found');
    }

    const assessmentData = assessmentDoc.data();

    // Log the data structure for debugging
    console.log('Assessment data structure:', {
      hasCompetencyAnalysis: !!assessmentData.competencyAnalysis,
      hasPrimaryRecs: !!assessmentData.courseRecommendations,
      hasOtherRecs: !!assessmentData.otherPathRecommendations,
      primaryRecsCount: assessmentData.courseRecommendations?.length,
      otherRecsCount: assessmentData.otherPathRecommendations?.length
    });

    return {
      report: {
        competencyAnalysis: assessmentData.competencyAnalysis,
        summary: assessmentData.analysisSummary
      },
      recommendations: assessmentData.courseRecommendations.map(rec => ({
        course: rec.courseName,
        reason: rec.justification
      })),
      other_learning_paths_courses: assessmentData.otherPathRecommendations?.map(rec => ({
        course: rec.courseName,
        reason: rec.justification,
        learningPath: rec.learningPath
      })) || []  // Provide empty array as fallback
    };
  } catch (error) {
    console.error('Error fetching assessment data:', error);
    throw error;
  }
}

let loadingAnimation = null;

function showLoadingOverlay() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (!loadingOverlay) return;

  // Initialize Lottie animation if not already done
  if (!loadingAnimation) {
    loadingAnimation = lottie.loadAnimation({
      container: document.getElementById('lottie-loading'),
      renderer: 'svg',
      loop: true,
      autoplay: true,
      path: 'assess_loading.json',
    });
    loadingAnimation.setSpeed(0.5);
  }

  let loadingTextDiv = loadingOverlay.querySelector(".loading-text");
  if (!loadingTextDiv) {
    loadingTextDiv = document.createElement("div");
    loadingTextDiv.className = "loading-text text-white";
    loadingOverlay.appendChild(loadingTextDiv);
  }

  loadingOverlay.style.display = "flex";
  loadingOverlay.style.opacity = "0";

  const messages = [
    'Saving your response...',
    'Just a moment...',
  ];
  let currentIndex = 0;

  loadingTextDiv.textContent = messages[currentIndex];

  const messageInterval = setInterval(() => {
    currentIndex = (currentIndex + 1) % messages.length;
    loadingTextDiv.textContent = messages[currentIndex];
  }, 3000);

  loadingOverlay.dataset.messageInterval = messageInterval;

  setTimeout(() => {
    loadingOverlay.style.opacity = "1";
  }, 10);
}

function hideLoadingOverlay() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (!loadingOverlay) return;

  if (loadingOverlay.dataset.messageInterval) {
    clearInterval(Number(loadingOverlay.dataset.messageInterval));
    delete loadingOverlay.dataset.messageInterval;
  }

  // Stop and destroy Lottie animation
  if (loadingAnimation) {
    loadingAnimation.stop();
    loadingAnimation.destroy();
    loadingAnimation = null;
  }

  loadingOverlay.style.opacity = "0";
  setTimeout(() => {
    loadingOverlay.style.display = "none";
  }, 300);
}

function showResultsSummary() {
  document.getElementById("results-summary-container").style.display = "flex";
  document.getElementById("failure-container").style.display = "none";

  const resultsSummaryContainer = document.getElementById("results-summary-container");
  resultsSummaryContainer.innerHTML = `
    <div class="summary-header">
      <h2>Results Summary</h2>
      <span class="close-btn">&times;</span>
    </div>
    <div class="summary-content">
      <div class="section-container"></div>
    </div>
  `;

  const sectionContainer = resultsSummaryContainer.querySelector(".section-container");

  for (const [sectionName, sectionScore] of Object.entries(sectionScores)) {
    if (sectionScore > 0 || currentSection === sectionNames.indexOf(sectionName) + 1) {
      const section = document.createElement("div");
      section.classList.add("section");

      const progress = (sectionScore / quizData.length) * 100;

      section.innerHTML = `
        <h3>Section ${sectionNames.indexOf(sectionName) + 1}: ${sectionName}</h3>
        <div class="score-container">
          <span class="user-score">${sectionScore}</span>
          <span class="total-questions">/</span>
          <span class="max-score">${quizData.length}</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: ${progress}%"></div>
        </div>
        <div class="remarks"></div>
      `;

      const remarks = section.querySelector(".remarks");
      if (progress >= 70) {
        remarks.textContent = "Great job! You've demonstrated a solid understanding of this section.";
        remarks.style.color = "#666";
      } else if (progress >= 50) {
        remarks.textContent = "Good effort! With some additional practice, you'll master this section.";
        remarks.style.color = "#666";
      } else {
        remarks.textContent = "There's room for improvement. Let's work on strengthening your knowledge in this area.";
        remarks.style.color = "#666";
      }

      sectionContainer.appendChild(section);
    }
  }

  const closeBtn = resultsSummaryContainer.querySelector(".close-btn");
  closeBtn.addEventListener("click", () => {
    document.getElementById("results-summary-container").style.display = "none";
    document.getElementById("failure-container").style.display = "block";
  });
}

const feedbackContainer = document.getElementById('feedback-container');
const feedbackForm = document.getElementById('feedback-form');
const pathwayBtn = document.getElementById('pathway-btn');


function showElement(elementId) {
  const element = document.getElementById(elementId);
  element.style.display = 'block';
  setTimeout(() => {
      element.classList.add('visible');
      element.style.height = 'auto';
  }, 10);
}


function hideElement(elementId) {
  const element = document.getElementById(elementId);
  element.classList.remove('visible');
  element.style.height = '0';
  setTimeout(() => {
      element.style.display = 'none';
  }, 300);
}


function showFeedbackPrompt() {
  showElement('feedback-container');
}

function hideFeedbackPrompt() {
  hideElement('feedback-container');
}

// Function to show the notification
function showNotification(message, type = 'success') {
  // Remove any existing notifications
  const existingNotifications = document.querySelectorAll('.notification');
  existingNotifications.forEach(notification => notification.remove());

  const notificationContainer = document.createElement('div');
  notificationContainer.classList.add('notification');
  notificationContainer.classList.add(type);
  notificationContainer.textContent = message;

  document.body.appendChild(notificationContainer);

  // Force reflow to ensure transition works
  notificationContainer.offsetHeight;

  // Fade in
  notificationContainer.style.opacity = '1';

  // Fade out and remove after delay
  setTimeout(() => {
    notificationContainer.style.opacity = '0';
    setTimeout(() => {
      notificationContainer.remove();
    }, 300);
  }, 4700);
}

document.getElementById('yes-feedback-btn').addEventListener('click', () => {
  document.getElementById('feedback-container').style.display = 'none';
  document.getElementById('feedback-form').style.display = 'block';
});

document.getElementById('no-feedback-btn').addEventListener('click', () => {
  document.getElementById('feedback-container').style.display = 'none';
  document.getElementById('pathway-btn').style.display = 'inline-block';
});



document.getElementById('submit-feedback').addEventListener('click', async () => {
  showLoadingOverlay();

  const feedbackQuestions = document.querySelectorAll('#feedback-form .feedback-question');

  let feedbackText = '';
  let hasAnswer = false;

  feedbackQuestions.forEach((question, index) => {
    const questionText = question.querySelector('p').textContent.trim();
    const selectedOption = question.querySelector('input:checked');

    if (selectedOption) {
      feedbackText += `Q${index + 1}: ${questionText}\nA: ${selectedOption.value}\n\n`;
      hasAnswer = true;
    }
  });

  console.log('Feedback collected:', feedbackText);
  console.log('Has answer:', hasAnswer);

  if (hasAnswer) {
    try {
      const feedbackStored = await storeFeedback(feedbackText);

      if (feedbackStored) {
        document.getElementById('feedback-form').style.display = 'none';

        setTimeout(() => {
          hideLoadingOverlay();
          document.getElementById('pathway-btn').style.display = 'inline-block';
          showNotification('Thank you for your feedback!', 'success');
        }, 300);
      } else {
        throw new Error('Failed to store feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      hideLoadingOverlay();
      showNotification('An error occurred while submitting feedback. Please try again.', 'error');
    }
  } else {
    hideLoadingOverlay();
    showNotification('Please answer at least one feedback question before submitting.', 'error');
  }
});

async function storeFeedback(feedback) {
  try {
    const companyRef = db.collection('companies').doc(userCompany);
    const feedbackRef = companyRef.collection('feedbacks');

    await feedbackRef.add({
      feedback: feedback,
      timestamp: firebase.firestore.FieldValue.serverTimestamp()
    });

    console.log('Feedback stored successfully');
    return true;
  } catch (error) {
    console.error('Error storing feedback:', error);
    return false;
  }
}

async function sendAssessmentResult(email) {
  const firstName = document.getElementById("first-name").value.trim();
  const lastName = document.getElementById("last-name").value.trim();
  const rawRole = document.getElementById("role").value.trim();
  const currentSectionName = sectionNames[currentSection - 1].toLowerCase();

  if (!userCompany) {
    console.error('userCompany is not set');
    throw new Error('Company information is missing');
  }

  // Get the session logs
  const sessionLogs = window.quizLogger.getSessionLogs();

  // Get all responses for the current section
  const currentSectionResponses = sessionLogs.userResponses.filter(response =>
    response.section.toLowerCase() === currentSectionName
  );

  // Separate responses by type
  const knowledgeResponses = currentSectionResponses.filter(r =>
    r.questionType === "knowledge-check" || !r.questionType
  );

  const selfAssessmentResponses = currentSectionResponses.filter(r =>
    r.questionType === "self-assessment"
  );

  // Log response counts for debugging
  console.log('Response counts:', {
    totalResponses: currentSectionResponses.length,
    knowledgeResponses: knowledgeResponses.length,
    selfAssessmentResponses: selfAssessmentResponses.length
  });

  // If no responses were logged but we have quiz data, create responses based on actual score
  if (currentSectionResponses.length === 0 && quizData && quizData.length > 0) {
    console.log('No responses were logged but quiz was completed. Creating responses based on actual score.');

    // Count how many knowledge check questions we have
    const knowledgeCheckQuestions = quizData.filter(q => q.type === "knowledge-check");
    const selfAssessmentQuestions = quizData.filter(q => q.type === "self-assessment");

    // Get the actual score from the UI (this is the number of correct answers)
    const actualScore = sectionScores[sectionNames[currentSection - 1]] || 0;
    console.log(`Actual score for section ${currentSectionName}: ${actualScore}/${knowledgeCheckQuestions.length}`);

    // Create responses based on quiz data and actual score
    let correctAnswersCreated = 0;

    // Process knowledge check questions first
    knowledgeCheckQuestions.forEach((question, index) => {
      // Determine if this answer should be marked correct based on actual score
      const shouldBeCorrect = correctAnswersCreated < actualScore;

      const defaultResponse = {
        question: question.question,
        // If this should be correct, use the correct answer, otherwise use a wrong answer
        selectedAnswer: shouldBeCorrect ? question.answer : getIncorrectAnswer(question),
        correctAnswer: question.answer,
        isCorrect: shouldBeCorrect,
        questionNumber: index + 1,
        questionType: "knowledge-check",
        skillArea: question.skillArea || question.course || "General",
        section: currentSectionName,
        timestamp: new Date().toISOString()
      };

      if (shouldBeCorrect) {
        correctAnswersCreated++;
      }

      knowledgeResponses.push(defaultResponse);
      currentSectionResponses.push(defaultResponse);
    });

    // Process self-assessment questions
    selfAssessmentQuestions.forEach((question, index) => {
      // For self-assessment, use the middle option (intermediate level)
      const options = question.options || ["Basic", "Intermediate", "Advanced"];
      const selectedIndex = Math.min(1, options.length - 1); // Default to intermediate (index 1) or last option

      const defaultResponse = {
        question: question.question,
        selectedAnswer: options[selectedIndex],
        options: options,
        questionNumber: index + 1,
        questionType: "self-assessment",
        skillLevel: selectedIndex + 1,
        skillArea: question.skillArea || question.course || "General",
        section: currentSectionName,
        timestamp: new Date().toISOString()
      };
      selfAssessmentResponses.push(defaultResponse);
      currentSectionResponses.push(defaultResponse);
    });

    console.log('Created responses based on actual score:', {
      totalResponses: currentSectionResponses.length,
      knowledgeResponses: knowledgeResponses.length,
      selfAssessmentResponses: selfAssessmentResponses.length,
      correctAnswers: correctAnswersCreated
    });
  }

  // Helper function to get an incorrect answer for a question
  function getIncorrectAnswer(question) {
    // Find an option that is not the correct answer
    const incorrectOptions = question.options.filter(option => option !== question.answer);
    // If there are no incorrect options (should never happen), return the first option
    return incorrectOptions.length > 0 ? incorrectOptions[0] : question.options[0];
  }

  const assessmentResult = {
    score: score,
    sectionScores: { [currentSectionName]: sectionScores[currentSectionName] },
    questionsPerSection: { [currentSectionName]: questionsPerSection[currentSectionName] },
    currentSection,
    learningPath: currentSectionName,
    framework: sessionLogs.framework,
    userResponses: currentSectionResponses,
    // Add explicit response type categorization
    responseTypes: {
      knowledgeCheck: knowledgeResponses,
      selfAssessment: selfAssessmentResponses
    },
    assessmentType: 'softSkills' // Add assessment type
  };

  try {
    const response = await fetch('/api/assessment-result', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userEmail: email,
        firstName,
        lastName,
        role: rawRole,
        userCompany,
        assessmentResult,
        assessmentType: 'softSkills' // Add assessment type at the top level
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to send result. Status: ${response.status}. Details: ${JSON.stringify(errorData)}`);
    }

    // Only start polling if it hasn't already been started
    if (!window.isPollingStarted) {
      window.isPollingStarted = true;
      pollForRecommendations(email, userCompany);
    }

    console.log('Assessment result sent successfully');
    return await response.json();
  } catch (error) {
    console.error('Error sending assessment result:', error);
    throw error;
  }
}

// Get role input for event listeners
const roleInput = document.getElementById('role');


let roleValidationTimeout = null;

const commonRolesSet = new Set(commonRoles.map(role => role.toLowerCase()));








// Clear role validation errors when user starts typing
roleInput.addEventListener('input', (event) => {
    clearRoleValidationError();
    updateSubmitButton(); // Update button state based on field content
});

// Update submit button state when first name changes
document.getElementById('first-name').addEventListener('input', (event) => {
    updateSubmitButton();
});

// Update submit button state when last name changes
document.getElementById('last-name').addEventListener('input', (event) => {
    updateSubmitButton();
});

// Duplicate email event listener removed - using the consolidated one above

// Duplicate event listeners removed - using the consolidated one above








// Modern Loading System Configuration
const LoadingState = {
  IDLE: 'idle',
  LOADING_FRAMEWORK: 'loading_framework',
  LOADING_FIRST_BATCH: 'loading_first_batch',
  LOADING_SELF_ASSESSMENT: 'loading_self_assessment',
  LOADING_ADDITIONAL_BATCHES: 'loading_additional_batches',
  COMPLETE: 'complete',
  ERROR: 'error'
};

const LoadingMessages = {
  [LoadingState.LOADING_FRAMEWORK]: {
    main: "Personalising your assessment...",
    sub: "Preparing your framework"
  },
  [LoadingState.LOADING_FIRST_BATCH]: {
    main: "Updating your questions...",
    sub: "Generating your first set"
  },
  [LoadingState.LOADING_SELF_ASSESSMENT]: {
    main: "Just a moment...",
    sub: "Creating self-assessment questions"
  },
  [LoadingState.LOADING_ADDITIONAL_BATCHES]: {
    main: "Finalising...",
    sub: "Loading additional questions"
  },
  [LoadingState.COMPLETE]: {
    main: "Assessment ready!",
    sub: "Let's begin your journey"
  },
  [LoadingState.ERROR]: {
    main: "Something went wrong",
    sub: "Please try again"
  }
};

let currentLoadingState = LoadingState.IDLE;
let loadingErrors = [];
let batchLoadingProgress = {};

// Modern Loading Function
function showQuizLoadingOverlay() {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');
  const batchProgressContainer = document.getElementById('batch-progress-container');

  // Reset any previous state
  window.actualLoadingStarted = false;
  if (loadingOverlay.dataset.gradualProgressInterval) {
    clearInterval(Number(loadingOverlay.dataset.gradualProgressInterval));
    delete loadingOverlay.dataset.gradualProgressInterval;
  }

  // Initialize progress ring at 0%
  if (progressRing) {
    progressRing.style.strokeDashoffset = 377; // 0% progress (2 * PI * 60)
  }
  if (progressPercentage) {
    progressPercentage.textContent = '0%';
  }

  // Hide batch progress initially
  if (batchProgressContainer) {
    batchProgressContainer.classList.remove('show');
  }

  // Show loading overlay with modern animation
  loadingOverlay.style.display = 'flex';

  // Use requestAnimationFrame to start as soon as the overlay is in the render tree
  requestAnimationFrame(() => {
    loadingOverlay.classList.add('show');

    // Force a reflow on the progress ring to ensure layout is ready before animating
    const ring = loadingOverlay.querySelector('.progress-ring-fill');
    if (ring) { ring.getBoundingClientRect(); }

    // Adjust for mobile if needed
    if (window.adjustLoadingForMobile) {
      window.adjustLoadingForMobile();
    }

    // Start immediate visual progress feedback within the next paint
    requestAnimationFrame(() => startImmediateProgress(true));
  });
}

function hideQuizLoadingOverlay() {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  // Clear any running intervals
  if (loadingOverlay.dataset.gradualProgressInterval) {
    clearInterval(Number(loadingOverlay.dataset.gradualProgressInterval));
    delete loadingOverlay.dataset.gradualProgressInterval;
  }

  // Show completion state briefly
  updateLoadingState(LoadingState.COMPLETE);
  updateLoadingProgress(100);

  // Hide overlay with animation
  setTimeout(() => {
    loadingOverlay.classList.remove('show');
    setTimeout(() => {
      loadingOverlay.style.display = 'none';

      // Reset for next use
      const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
      const progressPercentage = loadingOverlay.querySelector('.progress-percentage');
      const batchProgressContainer = document.getElementById('batch-progress-container');

      if (progressRing) progressRing.style.strokeDashoffset = 377;
      if (progressPercentage) progressPercentage.textContent = '0%';
      if (batchProgressContainer) batchProgressContainer.classList.remove('show');

      // Reset state
      window.actualLoadingStarted = false;
      currentLoadingState = LoadingState.IDLE;
    }, 400);
  }, 1000);
}

// Function to start immediate visual progress feedback
function startImmediateProgress(immediateBump = false) {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');

  if (!progressRing || !progressPercentage) return;

  const circumference = 377;

  // Optional immediate bump to show activity within ~<100ms of becoming visible
  if (immediateBump) {
    const initial = 5; // 5% right away
    const offset = circumference - (initial / 100) * circumference;
    progressRing.style.strokeDashoffset = offset;
    progressPercentage.textContent = `${initial}%`;
  }

  // Start with immediate progress animation to prevent "stuck" perception
  let currentProgress = 0;
  const targetInitialProgress = 15; // Initial target to show immediate activity
  const animationDuration = 500; // 500ms for initial progress
  const startTime = performance.now();

  function animateInitialProgress() {
    const elapsed = performance.now() - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);

    // Use easeOutCubic for natural deceleration
    const eased = 1 - Math.pow(1 - progress, 3);
    currentProgress = targetInitialProgress * eased;

    const offset = circumference - (currentProgress / 100) * circumference;
    progressRing.style.strokeDashoffset = offset;
    progressPercentage.textContent = `${Math.round(currentProgress)}%`;

    if (progress < 1) {
      requestAnimationFrame(animateInitialProgress);
    } else {
      // Start gradual progress after initial animation
      startGradualProgress(currentProgress);
    }
  }

  requestAnimationFrame(animateInitialProgress);
}

// Function to continue gradual progress after initial burst
function startGradualProgress(startProgress) {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');

  if (!progressRing || !progressPercentage) return;

  let currentProgress = startProgress;
  const targetProgress = 25; // Gradual target before actual loading takes over
  const incrementInterval = 100; // Update every 100ms
  const incrementAmount = 0.5; // Small increments to show activity

  const gradualInterval = setInterval(() => {
    if (window.actualLoadingStarted) {
      clearInterval(gradualInterval);
      return;
    }

    if (currentProgress < targetProgress) {
      currentProgress += incrementAmount;
      const circumference = 377;
      const offset = circumference - (currentProgress / 100) * circumference;
      progressRing.style.strokeDashoffset = offset;
      progressPercentage.textContent = `${Math.round(currentProgress)}%`;
    } else {
      clearInterval(gradualInterval);
    }
  }, incrementInterval);

  // Store interval reference for cleanup
  loadingOverlay.dataset.gradualProgressInterval = gradualInterval;
}

// Function to take over from visual progress with real loading data
function takeOverFromVisualProgress() {
  window.actualLoadingStarted = true;

  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (loadingOverlay && loadingOverlay.dataset.gradualProgressInterval) {
    clearInterval(Number(loadingOverlay.dataset.gradualProgressInterval));
    delete loadingOverlay.dataset.gradualProgressInterval;
  }
}

// Function to update loading progress with real data
function updateLoadingProgress(progress) {
  const loadingOverlay = document.getElementById('quiz-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('.progress-percentage');
  const circumference = 377; // 2 * PI * 60 (radius of our ring)

  if (progressRing) {
    const offset = circumference - (progress / 100) * circumference;
    progressRing.style.strokeDashoffset = offset;
  }

  if (progressPercentage) {
    progressPercentage.textContent = `${Math.round(progress)}%`;
  }
}

// Function to update loading state and provide user feedback
function updateLoadingState(newState, details = {}) {
  const previousState = currentLoadingState;
  currentLoadingState = newState;

  console.log(`Loading state changed: ${previousState} -> ${newState}`, details);

  const loadingMessage = document.getElementById('quiz-loading-message');
  const loadingSubmessage = document.getElementById('quiz-loading-submessage');

  if (!loadingMessage) return;

  // Get message configuration
  let messageConfig = LoadingMessages[newState];

  // Allow custom messages via details
  if (details.customMessage) {
    messageConfig = {
      main: details.customMessage,
      sub: details.customSubmessage || messageConfig?.sub || ''
    };
  }

  if (messageConfig) {
    // Fade out current message
    loadingMessage.style.opacity = '0';
    if (loadingSubmessage) {
      loadingSubmessage.style.opacity = '0';
    }

    // Update messages after fade out
    setTimeout(() => {
      loadingMessage.textContent = messageConfig.main;
      if (loadingSubmessage) {
        loadingSubmessage.textContent = messageConfig.sub;
      }

      // Fade in new messages
      loadingMessage.style.opacity = '1';
      if (loadingSubmessage) {
        setTimeout(() => {
          loadingSubmessage.style.opacity = '1';
        }, 100);
      }
    }, 200);
  }

  // Update step indicators
  updateStepIndicators(newState);

  // Show/hide batch progress based on state
  const batchProgressContainer = document.getElementById('batch-progress-container');
  if (batchProgressContainer) {
    if (newState === LoadingState.LOADING_ADDITIONAL_BATCHES) {
      batchProgressContainer.classList.add('show');
    } else {
      batchProgressContainer.classList.remove('show');
    }
  }
}

// Function to update step indicators
function updateStepIndicators(state) {
  const steps = document.querySelectorAll('.step');

  steps.forEach(step => {
    step.classList.remove('active', 'completed');
  });

  switch (state) {
    case LoadingState.LOADING_FRAMEWORK:
      steps[0]?.classList.add('active');
      break;
    case LoadingState.LOADING_FIRST_BATCH:
    case LoadingState.LOADING_SELF_ASSESSMENT:
    case LoadingState.LOADING_ADDITIONAL_BATCHES:
      steps[0]?.classList.add('completed');
      steps[1]?.classList.add('active');
      break;
    case LoadingState.COMPLETE:
      steps[0]?.classList.add('completed');
      steps[1]?.classList.add('completed');
      steps[2]?.classList.add('active');
      break;
  }
}

// Function to update batch loading progress
function updateBatchProgress(batchNumber, status, totalBatches = 3) {
  const batchProgressFill = document.getElementById('batch-progress-fill');
  const batchProgressText = document.getElementById('batch-progress-text');

  if (!batchProgressFill || !batchProgressText) return;

  // Update progress based on completed batches
  const completedBatches = status === 'completed' ? batchNumber : batchNumber - 1;
  const progressPercentage = (completedBatches / totalBatches) * 100;

  batchProgressFill.style.width = `${progressPercentage}%`;
  batchProgressText.textContent = `Batch ${batchNumber} of ${totalBatches}`;

  // Update loading state if we're in the additional batches loading state
  if (currentLoadingState === LoadingState.LOADING_ADDITIONAL_BATCHES) {
    updateLoadingState(LoadingState.LOADING_ADDITIONAL_BATCHES);
  }

  console.log(`Batch ${batchNumber} ${status}. Progress:`, batchLoadingProgress);
}

// Function to handle loading errors gracefully
function handleLoadingError(error, context = '') {
  console.error(`Loading error in ${context}:`, error);
  loadingErrors.push({ error: error.message, context, timestamp: Date.now() });

  // Show user-friendly error message
  const loadingMessage = document.getElementById('quiz-loading-message');
  if (loadingMessage) {
    loadingMessage.textContent = `Having trouble loading questions. Retrying...`;
  }

  // Update progress to show we're handling the error
  updateLoadingProgress(Math.max(currentProgress - 10, 10));

  // Update to error state if multiple failures
  if (loadingErrors.length > 2) {
    updateLoadingState(LoadingState.ERROR);
  }
}

// Modern Modal Loading Functions
function updateModalLoadingProgress(progress) {
  const loadingOverlay = document.getElementById('modal-loading-overlay');
  if (!loadingOverlay) return;

  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('#modal-progress-percentage');
  const circumference = 377; // 2 * PI * 60 (radius of our ring)

  if (progressRing) {
    const offset = circumference - (progress / 100) * circumference;
    progressRing.style.strokeDashoffset = offset;
  }

  if (progressPercentage) {
    progressPercentage.textContent = `${Math.round(progress)}%`;
  }
}

function showModalLoadingOverlay() {
  const loadingOverlay = document.getElementById('modal-loading-overlay');
  if (!loadingOverlay) return;

  // Initialize progress ring at 0%
  const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
  const progressPercentage = loadingOverlay.querySelector('#modal-progress-percentage');

  if (progressRing) {
    progressRing.style.strokeDashoffset = 377; // 0% progress
  }
  if (progressPercentage) {
    progressPercentage.textContent = '0%';
  }

  // Show loading overlay with modern animation
  loadingOverlay.style.display = 'flex';

  requestAnimationFrame(() => {
    loadingOverlay.classList.add('show');
  });

  // Simulate progress for modal
  let progress = 0;
  const interval = setInterval(() => {
    if (progress < 90) {
      progress += Math.random() * 15;
      progress = Math.min(90, progress);
      updateModalLoadingProgress(Math.round(progress));
    }
  }, 800);

  loadingOverlay.dataset.progressInterval = interval;
}

function hideModalLoadingOverlay() {
  const loadingOverlay = document.getElementById('modal-loading-overlay');
  if (!loadingOverlay) return;

  if (loadingOverlay.dataset.progressInterval) {
    clearInterval(Number(loadingOverlay.dataset.progressInterval));
    delete loadingOverlay.dataset.progressInterval;
  }

  // Show 100% completion before hiding
  updateModalLoadingProgress(100);

  // Update modal message to completion
  const modalMessage = document.getElementById('modal-loading-message');
  const modalSubmessage = document.getElementById('modal-loading-submessage');
  if (modalMessage) {
    modalMessage.textContent = 'Analysis complete!';
  }
  if (modalSubmessage) {
    modalSubmessage.textContent = 'Preparing your results';
  }

  setTimeout(() => {
    loadingOverlay.classList.remove('show');
    setTimeout(() => {
      loadingOverlay.style.display = 'none';

      // Reset for next use
      const progressRing = loadingOverlay.querySelector('.progress-ring-fill');
      const progressPercentage = loadingOverlay.querySelector('#modal-progress-percentage');

      if (progressRing) progressRing.style.strokeDashoffset = 377;
      if (progressPercentage) progressPercentage.textContent = '0%';
    }, 400);
  }, 1000);
}

// Add this new function
function standardizeRoleText(role) {
  // Trim whitespace and split into words
  const words = role.trim().split(/\s+/);
  // Capitalize first letter of each word
  return words.map(word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ');
}

// This DOMContentLoaded event listener is now consolidated in the main one at the bottom of the file



// Duplicate functions removed - using modern loading system above

/**
 * Deletes all documents in a Firestore collection
 * @param {object} collectionRef - Reference to a Firestore collection
 * @returns {Promise<number>} - Number of deleted documents
 */
async function deleteCollectionDocs(collectionRef) {
  try {
    const snapshot = await collectionRef.get();

    if (snapshot.empty) {
      console.log(`No documents found in collection to delete`);
      return 0;
    }

    const batch = db.batch();
    let count = 0;

    snapshot.forEach(doc => {
      batch.delete(doc.ref);
      count++;
    });

    await batch.commit();
    console.log(`Successfully deleted ${count} documents`);
    return count;
  } catch (error) {
    console.error('Error deleting collection docs:', error);
    throw error;
  }
}

/**
 * Deletes all previous soft skills assessment data for a user while preserving digital skills data
 * @param {string} email - User's email
 * @param {string} company - Company name
 * @returns {Promise<object>} - Count of deleted documents by collection
 */
async function deleteUserAssessmentData(email, company) {
  try {
    console.log(`Deleting previous soft skills assessment data for ${email} in ${company}`);

    // Set the flag to indicate a new assessment has started
    window.newAssessmentStarted = true;
    console.log('New assessment started flag set to true');

    const companyRef = db.collection('companies').doc(company);
    const userRef = companyRef.collection('users').doc(email);

    // First check if the user document exists
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      // If the user document doesn't exist, just create an empty one
      // We don't need to delete any assessment data because there isn't any
      console.log(`User document for ${email} doesn't exist. Creating a new one.`);
      await userRef.set({
        userEmail: email,
        userCompany: company,
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        status: 'new'
      });

      // Return early since there's no data to delete
      return {
        softSkillsResults: 0,
        softSkillsSummaries: 0
      };
    }

    // If the user document exists, proceed with clearing ONLY soft skills related fields
    await userRef.update({
      lastSoftSkillsAssessmentId: null,
      softSkillsCourseRecommendations: [],
      softSkillsOtherPathRecommendations: [],
      lastSoftSkillsAssessmentDate: null
      // Preserving digital skills fields:
      // courseRecommendations - contains digital skills data, don't clear
      // lastAssessmentId
      // otherPathRecommendations
      // lastAssessmentDate
    });

    console.log('Cleared soft skills recommendation fields in user document');

    // Delete ONLY soft skills assessment results
    const softSkillsResultsCount = await deleteCollectionDocs(userRef.collection('softSkillsAssessmentResults'));

    // Delete ONLY soft skills assessment summaries
    const softSkillsSummariesCount = await deleteCollectionDocs(userRef.collection('softSkillsSummaries'));

    console.log(`Deleted ${softSkillsResultsCount} soft skills results and ${softSkillsSummariesCount} soft skills summaries for ${email}. Digital skills data preserved.`);

    return {
      results: 0, // Set to 0 since we're not deleting digital skills results
      softSkillsResults: softSkillsResultsCount,
      summaries: 0, // Set to 0 since we're not deleting digital skills summaries
      softSkillsSummaries: softSkillsSummariesCount
    };
  } catch (error) {
    console.error(`Failed to delete assessment data for ${email}:`, error);
    throw error;
  }
}



  // Role suggestions setup
  createRoleSuggestions();

  // Form submission handler with submission-based validation
  const debouncedSubmitHandler = debounce(async (event) => {
    event.preventDefault();

    // Set intentional navigation flag
    intentionalNavigation = true;

    // Show validation loading overlay for email validation
    showValidationLoadingOverlay();

    const email = document.getElementById('email').value.trim();
    const rawRole = document.getElementById('role').value.trim();

    // Validate email first on submission
    const emailValidationResult = await validateEmailOnSubmit(email);

    if (!emailValidationResult.isValid) {
      hideValidationLoadingOverlay();
      showEmailValidationError(emailValidationResult.error);
      intentionalNavigation = false;
      return;
    }

    // Email is valid, now validate role
    const roleValidationResult = await validateRoleOnSubmit(rawRole);

    if (!roleValidationResult.isValid) {
      hideValidationLoadingOverlay();
      const errorMessage = roleValidationResult.error || 'Please enter a valid job role that represents a genuine occupation';
      showRoleValidationError(errorMessage);
      intentionalNavigation = false;
      return;
    }

    // Both email and role are valid - proceed with form submission
    hideValidationLoadingOverlay();
    showLoadingOverlay();

    // Set userCompany from email validation result
    userCompany = emailValidationResult.company;

    try {
      // Set the current assessment start time when starting a new assessment
      window.currentAssessmentStartTime = new Date();

      // Clear any existing polling when starting a new assessment
      if (window.pollInterval) {
        clearInterval(window.pollInterval);
        window.pollInterval = null;
        window.isPollingStarted = false;
      }

      // Delete all previous assessment data before starting a new assessment
      await deleteUserAssessmentData(email, userCompany);

      // Save form data to Firebase with standardized role
      const companyRef = db.collection('companies').doc(userCompany);
      const userRef = companyRef.collection('users').doc(email);

      await userRef.set({
        firstName: document.getElementById('first-name').value.trim(),
        lastName: document.getElementById('last-name').value.trim(),
        userEmail: email,
        userRole: standardizeRoleText(rawRole), // Standardize only when saving
        userCompany: userCompany,
        userPhone: document.getElementById('phone').value.trim() || null,
        status: 'started',
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        // Only clear soft skills related fields
        lastSoftSkillsAssessmentId: null, // Clear soft skills assessment ID
        lastSoftSkillsAssessmentDate: null, // Clear soft skills assessment date
        softSkillsCourseRecommendations: [], // Clear soft skills recommendations
        softSkillsOtherPathRecommendations: [] // Clear soft skills other path recommendations
        // Preserving digital skills fields
      }, { merge: true });

      // Hide loading overlay and form immediately after saving to database
      hideLoadingOverlay();
      document.getElementById('user-form-container').style.display = 'none';

      // Initialize framework with skeleton loading - use raw role for framework
      createSkillsGapAnalyzer('skills-gap-analyzer-container', {
        logoUrl: 'logosmall.png',
      });

      // Show framework container with skeleton loading
      document.getElementById('skills-gap-analyzer-container').classList.remove('hidden');

      // Fetch framework data using raw role
      window.fetchFrameworkData(rawRole).catch(error => {
        console.error('Error fetching framework data:', error);
        // Display error in content div
        const contentDiv = document.getElementById('content');
        if (contentDiv) {
          contentDiv.innerHTML = `
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
              Failed to generate framework. Please try again.
            </div>
          `;
        }
      });

      // Add event listener for framework's proceed button only if it hasn't been added before
      if (!window.proceedButtonListenerAdded) {
        window.proceedButtonListenerAdded = true;
        document.addEventListener('click', (e) => {
          if (e.target && e.target.id === 'proceed-to-assessment-btn') {
            // Set intentional navigation flag
            intentionalNavigation = true;

            // Set the current assessment start time when starting a new assessment
            window.currentAssessmentStartTime = new Date();

            // Set the flag to indicate a new assessment has started
            window.newAssessmentStarted = true;
            console.log('New assessment started flag set to true (from proceed button)');

            // Clear any existing polling when starting a new assessment
            if (window.pollInterval) {
              clearInterval(window.pollInterval);
              window.pollInterval = null;
              window.isPollingStarted = false;
            }

            document.getElementById('skills-gap-analyzer-container').classList.add('hidden');
            document.getElementById('quiz-container').style.display = 'block';
            currentQuestion = 0;
            score = 0;
            currentSection = 1;
            loadQuizData();
          }
        });
      }

    } catch (error) {
      console.error('Error processing user data:', error);
      alert('An error occurred while processing user data');
      hideLoadingOverlay();

      // Reset flag if there's an error
      intentionalNavigation = false;
    }
  }, 300);

  // Make sure we're properly preventing default form submission
  document.getElementById('user-form').addEventListener('submit', function(event) {
    // Prevent the default form submission
    event.preventDefault();

    // Then call our handler
    debouncedSubmitHandler(event);

    // Return false to ensure no submission happens
    return false;
  });
